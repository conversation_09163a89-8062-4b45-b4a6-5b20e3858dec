# Vehicle CRUD API

A RESTful API for managing vehicle inventory with user authentication built using Express.js, EJS templating, and Passport.js.

## Features

- **User Authentication**: Secure registration and login using Passport.js with local strategy
- **Vehicle CRUD Operations**: Create, Read, Update, Delete vehicles (authenticated users only)
- **User Management**: User registration with validation (username, email, password, age)
- **Responsive UI**: Bootstrap-powered frontend with EJS templating
- **Input Validation**: Comprehensive server-side validation for all inputs
- **Session Management**: Secure session handling with optional MongoDB store
- **Error Handling**: Graceful error handling with user-friendly messages

## Models

### User Model
- `username`: String (3-30 characters, unique)
- `email`: String (valid email format, unique)
- `password`: String (6+ characters, hashed with bcrypt)
- `age`: Number (13-120 years)

### Vehicle Model
- `vehicleName`: String (2-100 characters)
- `price`: Number (positive value)
- `image`: String (valid URL)
- `desc`: String (10-1000 characters)
- `brand`: String (2-50 characters)
- `createdBy`: ObjectId (reference to User)

## API Endpoints

### Authentication Routes
- `GET /register` - Registration form
- `POST /register` - Create new user account
- `GET /login` - Login form
- `POST /login` - Authenticate user
- `GET /logout` - Logout user

### Vehicle Routes (Protected)
- `GET /vehicles` - List all vehicles
- `GET /vehicles/new` - New vehicle form
- `POST /vehicles` - Create new vehicle
- `GET /vehicles/:id` - View single vehicle
- `GET /vehicles/:id/edit` - Edit vehicle form
- `PUT /vehicles/:id` - Update vehicle
- `DELETE /vehicles/:id` - Delete vehicle

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd vehicle-crud-api
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env` file in the root directory:
   ```env
   PORT=3000
   # For local MongoDB (install MongoDB locally)
   MONGODB_URI=mongodb://localhost:27017/vehicle_crud_db
   # For MongoDB Atlas (cloud database)
   # MONGODB_URI=mongodb+srv://username:<EMAIL>/vehicle_crud_db?retryWrites=true&w=majority
   SESSION_SECRET=your_super_secret_session_key_here_change_in_production
   NODE_ENV=development
   ```

4. **Set up MongoDB**
   - **Option 1**: Install MongoDB locally
   - **Option 2**: Use MongoDB Atlas (cloud database)
   - **Option 3**: Run without database (limited functionality for demo)

5. **Start the application**
   ```bash
   # Development mode with auto-restart
   npm run dev
   
   # Production mode
   npm start
   ```

6. **Access the application**
   Open your browser and navigate to `http://localhost:3000`

## Project Structure

```
vehicle-crud-api/
├── config/
│   ├── database.js          # Database connection configuration
│   └── passport.js          # Passport.js authentication strategy
├── middleware/
│   ├── auth.js              # Authentication middleware
│   └── validation.js        # Input validation middleware
├── models/
│   ├── User.js              # User model schema
│   └── Vehicle.js           # Vehicle model schema
├── routes/
│   ├── auth.js              # Authentication routes
│   └── vehicles.js          # Vehicle CRUD routes
├── views/
│   ├── auth/
│   │   ├── login.ejs        # Login form
│   │   └── register.ejs     # Registration form
│   ├── vehicles/
│   │   ├── index.ejs        # Vehicle list
│   │   ├── show.ejs         # Vehicle details
│   │   ├── new.ejs          # Add vehicle form
│   │   └── edit.ejs         # Edit vehicle form
│   ├── partials/
│   │   ├── header.ejs       # Header partial
│   │   └── footer.ejs       # Footer partial
│   ├── index.ejs            # Home page
│   ├── 404.ejs              # 404 error page
│   └── error.ejs            # General error page
├── public/
│   ├── css/
│   │   └── style.css        # Custom styles
│   └── js/
│       └── main.js          # Client-side JavaScript
├── app.js                   # Main application file
├── package.json             # Dependencies and scripts
└── README.md               # Project documentation
```

## Technologies Used

- **Backend**: Node.js, Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: Passport.js with local strategy
- **Templating**: EJS (Embedded JavaScript)
- **Styling**: Bootstrap 5, Font Awesome
- **Session Management**: express-session with connect-mongo
- **Password Hashing**: bcryptjs
- **Validation**: Custom middleware with comprehensive checks
- **Development**: nodemon for auto-restart

## Security Features

- Password hashing with bcrypt (cost factor: 12)
- Session-based authentication
- Input sanitization and validation
- CSRF protection through proper form handling
- Secure session configuration
- User authorization for resource access

## Usage

1. **Register a new account** at `/register`
2. **Login** with your credentials at `/login`
3. **View all vehicles** at `/vehicles`
4. **Add new vehicles** using the "Add Vehicle" button
5. **Edit or delete** your own vehicles from the vehicle list or detail pages
6. **Logout** using the dropdown menu

## Development

To contribute to this project:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the ISC License.

## Support

For support or questions, please open an issue in the repository.
