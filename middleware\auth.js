// Middleware to ensure user is authenticated
const ensureAuthenticated = (req, res, next) => {
    if (req.isAuthenticated()) {
        return next();
    }
    req.flash('error_msg', 'Please log in to access this page');
    res.redirect('/login');
};

// Middleware to ensure user is not authenticated (for login/register pages)
const ensureGuest = (req, res, next) => {
    if (req.isAuthenticated()) {
        res.redirect('/vehicles');
    } else {
        return next();
    }
};

// Middleware to check if user owns the resource
const ensureOwnership = (Model) => {
    return async (req, res, next) => {
        try {
            const resource = await Model.findById(req.params.id);
            
            if (!resource) {
                req.flash('error_msg', 'Resource not found');
                return res.redirect('/vehicles');
            }
            
            // Check if user owns the resource
            if (resource.createdBy.toString() !== req.user.id) {
                req.flash('error_msg', 'You are not authorized to perform this action');
                return res.redirect('/vehicles');
            }
            
            req.resource = resource;
            next();
        } catch (error) {
            console.error(error);
            req.flash('error_msg', 'An error occurred');
            res.redirect('/vehicles');
        }
    };
};

module.exports = {
    ensureAuthenticated,
    ensureGuest,
    ensureOwnership
};
