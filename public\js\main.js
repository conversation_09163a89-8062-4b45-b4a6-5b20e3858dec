// Main JavaScript file for Vehicle Manager

document.addEventListener('DOMContentLoaded', function() {
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert && alert.parentNode) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            }
        }, 5000);
    });

    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });

    // Form validation enhancement
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Loading...';
                
                // Re-enable button after 3 seconds in case of error
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }, 3000);
            }
        });
    });

    // Image preview for vehicle forms
    const imageInput = document.getElementById('image');
    if (imageInput) {
        imageInput.addEventListener('input', function() {
            const url = this.value;
            if (url) {
                // Create or update image preview
                let preview = document.getElementById('imagePreview');
                if (!preview) {
                    preview = document.createElement('div');
                    preview.id = 'imagePreview';
                    preview.className = 'mt-2';
                    this.parentNode.appendChild(preview);
                }
                
                preview.innerHTML = `
                    <img src="${url}" class="img-thumbnail" style="max-width: 200px; max-height: 150px;" 
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="text-danger small" style="display: none;">
                        <i class="fas fa-exclamation-triangle"></i> Unable to load image
                    </div>
                `;
            }
        });
    }

    // Confirm delete functionality
    window.confirmDelete = function(vehicleId, vehicleName) {
        if (confirm(`Are you sure you want to delete "${vehicleName}"? This action cannot be undone.`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/vehicles/${vehicleId}`;
            
            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';
            
            form.appendChild(methodInput);
            document.body.appendChild(form);
            form.submit();
        }
    };

    // Price formatting
    const priceInputs = document.querySelectorAll('input[name="price"]');
    priceInputs.forEach(input => {
        input.addEventListener('blur', function() {
            const value = parseFloat(this.value);
            if (!isNaN(value)) {
                this.value = value.toFixed(2);
            }
        });
    });

    // Character counter for description textarea
    const descTextarea = document.getElementById('desc');
    if (descTextarea) {
        const maxLength = 1000;
        const counter = document.createElement('div');
        counter.className = 'form-text text-end';
        counter.id = 'descCounter';
        descTextarea.parentNode.appendChild(counter);

        function updateCounter() {
            const remaining = maxLength - descTextarea.value.length;
            counter.textContent = `${descTextarea.value.length}/${maxLength} characters`;
            counter.className = remaining < 50 ? 'form-text text-end text-warning' : 'form-text text-end text-muted';
        }

        descTextarea.addEventListener('input', updateCounter);
        updateCounter(); // Initial count
    }
});
