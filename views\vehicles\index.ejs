<%- include('../partials/header', { title: 'All Vehicles - Vehicle Manager' }) %>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-car"></i> All Vehicles
    </h2>
    <a href="/vehicles/new" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add New Vehicle
    </a>
</div>

<% if (vehicles && vehicles.length > 0) { %>
    <div class="row">
        <% vehicles.forEach(vehicle => { %>
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <img src="<%= vehicle.image %>" class="card-img-top" alt="<%= vehicle.vehicleName %>" style="height: 200px; object-fit: cover;">
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">
                            <%= vehicle.vehicleName %>
                            <span class="badge bg-secondary ms-2"><%= vehicle.brand %></span>
                        </h5>
                        <p class="card-text text-muted flex-grow-1">
                            <%= vehicle.desc.length > 100 ? vehicle.desc.substring(0, 100) + '...' : vehicle.desc %>
                        </p>
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="h5 text-success mb-0">
                                    $<%= vehicle.price.toLocaleString() %>
                                </span>
                                <small class="text-muted">
                                    by <%= vehicle.createdBy.username %>
                                </small>
                            </div>
                            <div class="btn-group w-100" role="group">
                                <a href="/vehicles/<%= vehicle._id %>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <% if (user && vehicle.createdBy._id.toString() === user.id) { %>
                                    <a href="/vehicles/<%= vehicle._id %>/edit" class="btn btn-outline-warning btn-sm">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="confirmDelete('<%= vehicle._id %>', '<%= vehicle.vehicleName %>')">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                <% } %>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <% }) %>
    </div>
<% } else { %>
    <div class="text-center py-5">
        <i class="fas fa-car fa-5x text-muted mb-3"></i>
        <h3 class="text-muted">No vehicles found</h3>
        <p class="text-muted mb-4">Start building your vehicle inventory by adding your first vehicle.</p>
        <a href="/vehicles/new" class="btn btn-primary btn-lg">
            <i class="fas fa-plus"></i> Add Your First Vehicle
        </a>
    </div>
<% } %>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="vehicleNameToDelete"></strong>?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(vehicleId, vehicleName) {
    document.getElementById('vehicleNameToDelete').textContent = vehicleName;
    document.getElementById('deleteForm').action = '/vehicles/' + vehicleId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<%- include('../partials/footer') %>
