<%- include('../partials/header', { title: 'Register - Vehicle Manager' }) %>

<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow">
            <div class="card-header bg-success text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-user-plus"></i> Register
                </h4>
            </div>
            <div class="card-body">
                <!-- Display errors -->
                <% if (typeof errors !== 'undefined' && errors.length > 0) { %>
                    <div class="alert alert-danger">
                        <% errors.forEach(error => { %>
                            <div><%= error.msg %></div>
                        <% }) %>
                    </div>
                <% } %>

                <form action="/register" method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user"></i> Username
                        </label>
                        <input 
                            type="text" 
                            class="form-control" 
                            id="username" 
                            name="username" 
                            value="<%= typeof username !== 'undefined' ? username : '' %>"
                            required
                        >
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope"></i> Email
                        </label>
                        <input 
                            type="email" 
                            class="form-control" 
                            id="email" 
                            name="email" 
                            value="<%= typeof email !== 'undefined' ? email : '' %>"
                            required
                        >
                    </div>

                    <div class="mb-3">
                        <label for="age" class="form-label">
                            <i class="fas fa-calendar"></i> Age
                        </label>
                        <input 
                            type="number" 
                            class="form-control" 
                            id="age" 
                            name="age" 
                            min="13" 
                            max="120"
                            value="<%= typeof age !== 'undefined' ? age : '' %>"
                            required
                        >
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock"></i> Password
                        </label>
                        <input 
                            type="password" 
                            class="form-control" 
                            id="password" 
                            name="password" 
                            required
                        >
                    </div>

                    <div class="mb-3">
                        <label for="password2" class="form-label">
                            <i class="fas fa-lock"></i> Confirm Password
                        </label>
                        <input 
                            type="password" 
                            class="form-control" 
                            id="password2" 
                            name="password2" 
                            required
                        >
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-user-plus"></i> Register
                        </button>
                    </div>
                </form>

                <div class="text-center mt-3">
                    <p class="text-muted">
                        Already have an account? 
                        <a href="/login" class="text-decoration-none">Login here</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<%- include('../partials/footer') %>
