// Validation middleware and utilities

const validateVehicle = (req, res, next) => {
    const { vehicleName, price, image, desc, brand } = req.body;
    let errors = [];

    // Required fields validation
    if (!vehicleName || vehicleName.trim().length === 0) {
        errors.push({ msg: 'Vehicle name is required' });
    } else if (vehicleName.trim().length < 2) {
        errors.push({ msg: 'Vehicle name must be at least 2 characters long' });
    } else if (vehicleName.trim().length > 100) {
        errors.push({ msg: 'Vehicle name cannot exceed 100 characters' });
    }

    if (!brand || brand.trim().length === 0) {
        errors.push({ msg: 'Brand is required' });
    } else if (brand.trim().length < 2) {
        errors.push({ msg: 'Brand name must be at least 2 characters long' });
    } else if (brand.trim().length > 50) {
        errors.push({ msg: 'Brand name cannot exceed 50 characters' });
    }

    if (!price) {
        errors.push({ msg: 'Price is required' });
    } else {
        const priceNum = parseFloat(price);
        if (isNaN(priceNum)) {
            errors.push({ msg: 'Price must be a valid number' });
        } else if (priceNum < 0) {
            errors.push({ msg: 'Price cannot be negative' });
        } else if (priceNum > 10000000) {
            errors.push({ msg: 'Price cannot exceed $10,000,000' });
        }
    }

    if (!image || image.trim().length === 0) {
        errors.push({ msg: 'Image URL is required' });
    } else {
        const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
        if (!urlPattern.test(image.trim())) {
            errors.push({ msg: 'Please enter a valid image URL' });
        }
    }

    if (!desc || desc.trim().length === 0) {
        errors.push({ msg: 'Description is required' });
    } else if (desc.trim().length < 10) {
        errors.push({ msg: 'Description must be at least 10 characters long' });
    } else if (desc.trim().length > 1000) {
        errors.push({ msg: 'Description cannot exceed 1000 characters' });
    }

    if (errors.length > 0) {
        req.validationErrors = errors;
    }

    next();
};

const validateUser = (req, res, next) => {
    const { username, email, password, password2, age } = req.body;
    let errors = [];

    // Required fields validation
    if (!username || username.trim().length === 0) {
        errors.push({ msg: 'Username is required' });
    } else if (username.trim().length < 3) {
        errors.push({ msg: 'Username must be at least 3 characters long' });
    } else if (username.trim().length > 30) {
        errors.push({ msg: 'Username cannot exceed 30 characters' });
    } else if (!/^[a-zA-Z0-9_]+$/.test(username.trim())) {
        errors.push({ msg: 'Username can only contain letters, numbers, and underscores' });
    }

    if (!email || email.trim().length === 0) {
        errors.push({ msg: 'Email is required' });
    } else {
        const emailPattern = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
        if (!emailPattern.test(email.trim())) {
            errors.push({ msg: 'Please enter a valid email address' });
        }
    }

    if (!password) {
        errors.push({ msg: 'Password is required' });
    } else if (password.length < 6) {
        errors.push({ msg: 'Password must be at least 6 characters long' });
    } else if (password.length > 128) {
        errors.push({ msg: 'Password cannot exceed 128 characters' });
    }

    if (!password2) {
        errors.push({ msg: 'Please confirm your password' });
    } else if (password !== password2) {
        errors.push({ msg: 'Passwords do not match' });
    }

    if (!age) {
        errors.push({ msg: 'Age is required' });
    } else {
        const ageNum = parseInt(age);
        if (isNaN(ageNum)) {
            errors.push({ msg: 'Age must be a valid number' });
        } else if (ageNum < 13) {
            errors.push({ msg: 'You must be at least 13 years old to register' });
        } else if (ageNum > 120) {
            errors.push({ msg: 'Please enter a valid age' });
        }
    }

    if (errors.length > 0) {
        req.validationErrors = errors;
    }

    next();
};

const validateLogin = (req, res, next) => {
    const { username, password } = req.body;
    let errors = [];

    if (!username || username.trim().length === 0) {
        errors.push({ msg: 'Username is required' });
    }

    if (!password || password.length === 0) {
        errors.push({ msg: 'Password is required' });
    }

    if (errors.length > 0) {
        req.validationErrors = errors;
    }

    next();
};

// Sanitize input data
const sanitizeInput = (req, res, next) => {
    if (req.body) {
        Object.keys(req.body).forEach(key => {
            if (typeof req.body[key] === 'string') {
                // Trim whitespace
                req.body[key] = req.body[key].trim();
                
                // Basic XSS protection - remove script tags
                req.body[key] = req.body[key].replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
            }
        });
    }
    next();
};

module.exports = {
    validateVehicle,
    validateUser,
    validateLogin,
    sanitizeInput
};
