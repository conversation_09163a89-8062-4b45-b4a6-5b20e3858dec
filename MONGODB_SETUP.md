# MongoDB Atlas Setup Guide

This guide will help you set up MongoDB Atlas (cloud database) for the Vehicle CRUD API.

## Why MongoDB Atlas?

MongoDB Atlas is MongoDB's cloud database service that offers:
- ✅ Free tier (512MB storage)
- ✅ No installation required
- ✅ Automatic backups
- ✅ Global availability
- ✅ Easy setup

## Step-by-Step Setup

### 1. Create MongoDB Atlas Account

1. Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Click "Try Free" or "Sign Up"
3. Create your account with email/password or Google/GitHub

### 2. Create a New Cluster

1. After logging in, click "Create a New Cluster"
2. Choose the **FREE** tier (M0 Sandbox)
3. Select a cloud provider and region (choose closest to you)
4. Give your cluster a name (e.g., "VehicleCluster")
5. Click "Create Cluster" (takes 1-3 minutes)

### 3. Create Database User

1. In the Atlas dashboard, go to "Database Access" (left sidebar)
2. Click "Add New Database User"
3. Choose "Password" authentication
4. Create username and password (remember these!)
5. Set privileges to "Read and write to any database"
6. Click "Add User"

### 4. Configure Network Access

1. Go to "Network Access" (left sidebar)
2. Click "Add IP Address"
3. Choose "Allow Access from Anywhere" (0.0.0.0/0) for development
4. Click "Confirm"

### 5. Get Connection String

1. Go to "Clusters" (left sidebar)
2. Click "Connect" on your cluster
3. Choose "Connect your application"
4. Select "Node.js" and version "4.1 or later"
5. Copy the connection string

### 6. Update Your .env File

Replace the MONGODB_URI in your `.env` file:

```env
MONGODB_URI=mongodb+srv://<username>:<password>@<cluster-name>.mongodb.net/vehicle_crud_db?retryWrites=true&w=majority
```

**Important**: Replace:
- `<username>` with your database username
- `<password>` with your database password
- `<cluster-name>` with your actual cluster name

### Example:
If your username is "john", password is "mypass123", and cluster is "vehiclecluster.abc123":

```env
MONGODB_URI=mongodb+srv://john:<EMAIL>/vehicle_crud_db?retryWrites=true&w=majority
```

## Alternative: Use Demo Database

For quick testing, you can use this demo connection string (limited functionality):

```env
MONGODB_URI=mongodb+srv://demo:<EMAIL>/vehicle_crud_db?retryWrites=true&w=majority
```

**Note**: This is a shared demo database and may have limitations.

## Verify Connection

1. Update your `.env` file with the connection string
2. Restart the application: `npm run dev`
3. Look for "✅ Connected to MongoDB successfully!" in the console

## Troubleshooting

### Common Issues:

1. **Authentication Failed**
   - Double-check username and password
   - Ensure no special characters in password (or URL encode them)

2. **Network Timeout**
   - Verify IP address is whitelisted (0.0.0.0/0 for development)
   - Check your internet connection

3. **Invalid Connection String**
   - Ensure you copied the complete string
   - Replace `<username>`, `<password>`, and `<cluster-name>` with actual values

### URL Encoding Special Characters:

If your password contains special characters, encode them:
- `@` becomes `%40`
- `#` becomes `%23`
- `%` becomes `%25`
- etc.

## Security Best Practices

For production:
1. Use specific IP addresses instead of 0.0.0.0/0
2. Create separate users for different environments
3. Use strong, unique passwords
4. Enable two-factor authentication on your Atlas account

## Free Tier Limitations

- 512MB storage
- Shared RAM and CPU
- No backups (in free tier)
- Limited to 100 connections

For production applications, consider upgrading to a paid tier.

## Need Help?

- [MongoDB Atlas Documentation](https://docs.atlas.mongodb.com/)
- [MongoDB University (Free Courses)](https://university.mongodb.com/)
- [Community Forums](https://community.mongodb.com/)
