#!/usr/bin/env node

/**
 * MongoDB Atlas Setup Helper
 * This script helps you set up MongoDB Atlas connection for the Vehicle CRUD API
 */

const fs = require('fs');
const path = require('path');

console.log('🚗 Vehicle CRUD API - MongoDB Setup Helper\n');

console.log('This application is currently running in DEMO MODE without database.');
console.log('To enable full functionality with user registration and vehicle management,');
console.log('you need to connect to MongoDB Atlas (free cloud database).\n');

console.log('📋 Quick Setup Steps:');
console.log('1. Go to https://www.mongodb.com/atlas');
console.log('2. Sign up for a free account');
console.log('3. Create a new cluster (choose FREE tier M0)');
console.log('4. Create a database user');
console.log('5. Whitelist your IP address (or use 0.0.0.0/0 for development)');
console.log('6. Get your connection string');
console.log('7. Update the .env file with your connection string\n');

console.log('📖 For detailed instructions, see: MONGODB_SETUP.md\n');

console.log('🔧 Current .env configuration:');
const envPath = path.join(__dirname, '.env');
if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    lines.forEach(line => {
        if (line.includes('MONGODB_URI')) {
            console.log(`   ${line}`);
        }
    });
} else {
    console.log('   .env file not found');
}

console.log('\n💡 Example connection string:');
console.log('   MONGODB_URI=mongodb+srv://username:<EMAIL>/vehicle_crud_db?retryWrites=true&w=majority');

console.log('\n✅ Once you update the .env file with your MongoDB Atlas connection string:');
console.log('   1. Save the .env file');
console.log('   2. Restart the application (npm run dev)');
console.log('   3. Look for "✅ Connected to MongoDB successfully!" message');
console.log('   4. You can then register users and manage vehicles!\n');

console.log('🌐 The application is currently accessible at: http://localhost:3000');
console.log('📚 Documentation: README.md');
console.log('🔧 MongoDB Setup Guide: MONGODB_SETUP.md\n');

// Check if MongoDB URI is set
const envContent = fs.readFileSync(envPath, 'utf8');
if (envContent.includes('MONGODB_URI=mongodb') && !envContent.includes('# MONGODB_URI=')) {
    console.log('✅ MongoDB URI is configured in .env file');
} else {
    console.log('⚠️  MongoDB URI is not configured. Update .env file to enable database features.');
}

console.log('\n🚀 Happy coding!');
