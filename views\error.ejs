<%- include('partials/header', { title: '<PERSON>rro<PERSON> - Vehicle Manager' }) %>

<div class="text-center py-5">
    <i class="fas fa-exclamation-circle fa-5x text-danger mb-4"></i>
    <h1 class="display-4 mb-3">Oops!</h1>
    <h2 class="mb-3">Something went wrong</h2>
    <p class="lead text-muted mb-4">
        We encountered an unexpected error. Please try again later.
    </p>
    
    <% if (typeof error !== 'undefined' && process.env.NODE_ENV === 'development') { %>
        <div class="alert alert-danger text-start mt-4">
            <h5>Error Details (Development Mode):</h5>
            <pre><%= error.stack %></pre>
        </div>
    <% } %>
    
    <div class="d-flex justify-content-center gap-3">
        <a href="/" class="btn btn-primary">
            <i class="fas fa-home"></i> Go Home
        </a>
        <button onclick="history.back()" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Go Back
        </button>
    </div>
</div>

<%- include('partials/footer') %>
