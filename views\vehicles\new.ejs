<%- include('../partials/header', { title: 'Add Vehicle - Vehicle Manager' }) %>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-plus"></i> Add New Vehicle
                </h4>
            </div>
            <div class="card-body">
                <!-- Display errors -->
                <% if (typeof errors !== 'undefined' && errors.length > 0) { %>
                    <div class="alert alert-danger">
                        <% errors.forEach(error => { %>
                            <div><%= error.msg %></div>
                        <% }) %>
                    </div>
                <% } %>

                <form action="/vehicles" method="POST">
                    <div class="mb-3">
                        <label for="vehicleName" class="form-label">
                            <i class="fas fa-car"></i> Vehicle Name *
                        </label>
                        <input 
                            type="text" 
                            class="form-control" 
                            id="vehicleName" 
                            name="vehicleName" 
                            value="<%= typeof vehicleName !== 'undefined' ? vehicleName : '' %>"
                            placeholder="e.g., Toyota Camry 2023"
                            required
                        >
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="brand" class="form-label">
                                    <i class="fas fa-tag"></i> Brand *
                                </label>
                                <input 
                                    type="text" 
                                    class="form-control" 
                                    id="brand" 
                                    name="brand" 
                                    value="<%= typeof brand !== 'undefined' ? brand : '' %>"
                                    placeholder="e.g., Toyota"
                                    required
                                >
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">
                                    <i class="fas fa-dollar-sign"></i> Price *
                                </label>
                                <input 
                                    type="number" 
                                    class="form-control" 
                                    id="price" 
                                    name="price" 
                                    min="0" 
                                    step="0.01"
                                    value="<%= typeof price !== 'undefined' ? price : '' %>"
                                    placeholder="25000"
                                    required
                                >
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="image" class="form-label">
                            <i class="fas fa-image"></i> Image URL *
                        </label>
                        <input 
                            type="url" 
                            class="form-control" 
                            id="image" 
                            name="image" 
                            value="<%= typeof image !== 'undefined' ? image : '' %>"
                            placeholder="https://example.com/vehicle-image.jpg"
                            required
                        >
                        <div class="form-text">
                            Provide a valid URL to an image of the vehicle
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="desc" class="form-label">
                            <i class="fas fa-align-left"></i> Description *
                        </label>
                        <textarea 
                            class="form-control" 
                            id="desc" 
                            name="desc" 
                            rows="4" 
                            placeholder="Describe the vehicle's features, condition, and other relevant details..."
                            required
                        ><%= typeof desc !== 'undefined' ? desc : '' %></textarea>
                        <div class="form-text">
                            Minimum 10 characters, maximum 1000 characters
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Add Vehicle
                        </button>
                        <a href="/vehicles" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<%- include('../partials/footer') %>
