{"name": "vehicle-crud-api", "version": "1.0.0", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["vehicle", "crud", "api", "authentication", "express"], "author": "", "license": "ISC", "description": "RESTful API for Vehicle CRUD operations with authentication", "dependencies": {"bcryptjs": "^3.0.2", "connect-flash": "^0.1.1", "connect-mongo": "^5.1.0", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^5.1.0", "express-session": "^1.18.1", "method-override": "^3.0.0", "mongoose": "^8.16.0", "passport": "^0.7.0", "passport-local": "^1.0.0"}, "devDependencies": {"nodemon": "^3.1.10"}}