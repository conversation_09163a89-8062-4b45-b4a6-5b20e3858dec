const mongoose = require('mongoose');

const vehicleSchema = new mongoose.Schema({
    vehicleName: {
        type: String,
        required: [true, 'Vehicle name is required'],
        trim: true,
        minlength: [2, 'Vehicle name must be at least 2 characters long'],
        maxlength: [100, 'Vehicle name cannot exceed 100 characters']
    },
    price: {
        type: Number,
        required: [true, 'Price is required'],
        min: [0, 'Price cannot be negative'],
        validate: {
            validator: function(value) {
                return value >= 0;
            },
            message: 'Price must be a positive number'
        }
    },
    image: {
        type: String,
        required: [true, 'Image URL is required'],
        trim: true,
        validate: {
            validator: function(value) {
                // Basic URL validation
                const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
                return urlPattern.test(value);
            },
            message: 'Please enter a valid image URL'
        }
    },
    desc: {
        type: String,
        required: [true, 'Description is required'],
        trim: true,
        minlength: [10, 'Description must be at least 10 characters long'],
        maxlength: [1000, 'Description cannot exceed 1000 characters']
    },
    brand: {
        type: String,
        required: [true, 'Brand is required'],
        trim: true,
        minlength: [2, 'Brand name must be at least 2 characters long'],
        maxlength: [50, 'Brand name cannot exceed 50 characters']
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    }
}, {
    timestamps: true
});

// Index for better query performance
vehicleSchema.index({ brand: 1 });
vehicleSchema.index({ price: 1 });
vehicleSchema.index({ createdBy: 1 });

// Virtual for formatted price
vehicleSchema.virtual('formattedPrice').get(function() {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(this.price);
});

// Ensure virtual fields are serialized
vehicleSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('Vehicle', vehicleSchema);
