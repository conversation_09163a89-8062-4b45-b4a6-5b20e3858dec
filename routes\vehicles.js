const express = require('express');
const Vehicle = require('../models/Vehicle');
const { ensureAuthenticated, ensureOwnership } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(ensureAuthenticated);

// GET /vehicles - Display all vehicles
router.get('/', async (req, res) => {
    try {
        const vehicles = await Vehicle.find().populate('createdBy', 'username').sort({ createdAt: -1 });
        res.render('vehicles/index', { vehicles });
    } catch (error) {
        console.error(error);
        req.flash('error_msg', 'Error fetching vehicles');
        res.render('vehicles/index', { vehicles: [] });
    }
});

// GET /vehicles/new - Show form to create new vehicle
router.get('/new', (req, res) => {
    res.render('vehicles/new');
});

// POST /vehicles - Create new vehicle
router.post('/', async (req, res) => {
    const { vehicleName, price, image, desc, brand } = req.body;
    let errors = [];

    // Validation
    if (!vehicleName || !price || !image || !desc || !brand) {
        errors.push({ msg: 'Please fill in all fields' });
    }

    if (price && (isNaN(price) || parseFloat(price) < 0)) {
        errors.push({ msg: 'Price must be a positive number' });
    }

    if (errors.length > 0) {
        res.render('vehicles/new', {
            errors,
            vehicleName,
            price,
            image,
            desc,
            brand
        });
    } else {
        try {
            const newVehicle = new Vehicle({
                vehicleName,
                price: parseFloat(price),
                image,
                desc,
                brand,
                createdBy: req.user.id
            });

            await newVehicle.save();
            req.flash('success_msg', 'Vehicle added successfully');
            res.redirect('/vehicles');
        } catch (error) {
            console.error(error);
            if (error.name === 'ValidationError') {
                Object.values(error.errors).forEach(err => {
                    errors.push({ msg: err.message });
                });
                res.render('vehicles/new', {
                    errors,
                    vehicleName,
                    price,
                    image,
                    desc,
                    brand
                });
            } else {
                req.flash('error_msg', 'Error creating vehicle');
                res.redirect('/vehicles/new');
            }
        }
    }
});

// GET /vehicles/:id - Show single vehicle
router.get('/:id', async (req, res) => {
    try {
        const vehicle = await Vehicle.findById(req.params.id).populate('createdBy', 'username');
        
        if (!vehicle) {
            req.flash('error_msg', 'Vehicle not found');
            return res.redirect('/vehicles');
        }

        res.render('vehicles/show', { vehicle });
    } catch (error) {
        console.error(error);
        req.flash('error_msg', 'Error fetching vehicle');
        res.redirect('/vehicles');
    }
});

// GET /vehicles/:id/edit - Show edit form
router.get('/:id/edit', ensureOwnership(Vehicle), (req, res) => {
    res.render('vehicles/edit', { vehicle: req.resource });
});

// PUT /vehicles/:id - Update vehicle
router.put('/:id', ensureOwnership(Vehicle), async (req, res) => {
    const { vehicleName, price, image, desc, brand } = req.body;
    let errors = [];

    // Validation
    if (!vehicleName || !price || !image || !desc || !brand) {
        errors.push({ msg: 'Please fill in all fields' });
    }

    if (price && (isNaN(price) || parseFloat(price) < 0)) {
        errors.push({ msg: 'Price must be a positive number' });
    }

    if (errors.length > 0) {
        res.render('vehicles/edit', {
            errors,
            vehicle: { ...req.resource.toObject(), vehicleName, price, image, desc, brand }
        });
    } else {
        try {
            await Vehicle.findByIdAndUpdate(req.params.id, {
                vehicleName,
                price: parseFloat(price),
                image,
                desc,
                brand
            });

            req.flash('success_msg', 'Vehicle updated successfully');
            res.redirect(`/vehicles/${req.params.id}`);
        } catch (error) {
            console.error(error);
            if (error.name === 'ValidationError') {
                Object.values(error.errors).forEach(err => {
                    errors.push({ msg: err.message });
                });
                res.render('vehicles/edit', {
                    errors,
                    vehicle: { ...req.resource.toObject(), vehicleName, price, image, desc, brand }
                });
            } else {
                req.flash('error_msg', 'Error updating vehicle');
                res.redirect(`/vehicles/${req.params.id}/edit`);
            }
        }
    }
});

// DELETE /vehicles/:id - Delete vehicle
router.delete('/:id', ensureOwnership(Vehicle), async (req, res) => {
    try {
        await Vehicle.findByIdAndDelete(req.params.id);
        req.flash('success_msg', 'Vehicle deleted successfully');
        res.redirect('/vehicles');
    } catch (error) {
        console.error(error);
        req.flash('error_msg', 'Error deleting vehicle');
        res.redirect('/vehicles');
    }
});

module.exports = router;
