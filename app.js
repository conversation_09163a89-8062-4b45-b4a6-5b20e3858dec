require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const session = require('express-session');
const MongoStore = require('connect-mongo');
const passport = require('passport');
const methodOverride = require('method-override');
const path = require('path');
const flash = require('connect-flash');

const app = express();

// Database connection
let isMongoConnected = false;

if (process.env.MONGODB_URI && process.env.MONGODB_URI.includes('mongodb')) {
    mongoose.connect(process.env.MONGODB_URI)
    .then(() => {
        console.log('✅ Connected to MongoDB successfully!');
        console.log('Database features are now available.');
        isMongoConnected = true;
    })
    .catch(err => {
        console.error('❌ MongoDB connection failed:', err.message);
        console.log('📝 The application will continue running in demo mode.');
        console.log('');
        console.log('🔧 To enable full database functionality:');
        console.log('1. Visit: https://www.mongodb.com/atlas');
        console.log('2. Create a free account and cluster');
        console.log('3. Get your connection string');
        console.log('4. Update MONGODB_URI in .env file');
        console.log('5. See MONGODB_SETUP.md for detailed instructions');
        isMongoConnected = false;
    });
} else {
    console.log('⚠️  No valid MONGODB_URI found.');
    console.log('📝 Running in demo mode without database.');
    console.log('💡 See MONGODB_SETUP.md for setup instructions.');
    isMongoConnected = false;
}

// Middleware
app.use(express.urlencoded({ extended: true }));
app.use(express.json());
app.use(methodOverride('_method'));
app.use(express.static(path.join(__dirname, 'public')));

// Session configuration
const sessionConfig = {
    secret: process.env.SESSION_SECRET,
    resave: false,
    saveUninitialized: false,
    cookie: {
        maxAge: 1000 * 60 * 60 * 24 // 24 hours
    }
};

// Use MongoDB store if MongoDB URI is available and valid
if (process.env.MONGODB_URI && process.env.MONGODB_URI.includes('mongodb')) {
    try {
        sessionConfig.store = MongoStore.create({
            mongoUrl: process.env.MONGODB_URI,
            touchAfter: 24 * 3600, // lazy session update
            autoRemove: 'native' // auto remove expired sessions
        });
        console.log('✅ Session store configured with MongoDB');
    } catch (error) {
        console.log('⚠️  Using memory store for sessions');
    }
} else {
    console.log('⚠️  Using memory store for sessions (demo mode)');
}

app.use(session(sessionConfig));

// Passport configuration
require('./config/passport')(passport);

// Passport middleware
app.use(passport.initialize());
app.use(passport.session());

// Flash middleware
app.use(flash());

// View engine setup
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Global middleware to pass user and flash messages to all views
app.use((req, res, next) => {
    res.locals.user = req.user;
    res.locals.success_msg = req.flash('success_msg');
    res.locals.error_msg = req.flash('error_msg');
    res.locals.error = req.flash('error');
    next();
});

// Routes
app.use('/', require('./routes/auth'));
app.use('/vehicles', require('./routes/vehicles'));

// Home route
app.get('/', (req, res) => {
    res.render('index');
});

// 404 handler
app.use((req, res) => {
    res.status(404).render('404');
});

// Error handler
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).render('error', { error: err });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});
