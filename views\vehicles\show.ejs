<%- include('../partials/header', { title: vehicle.vehicleName + ' - Vehicle Manager' }) %>

<div class="row">
    <div class="col-md-6">
        <img src="<%= vehicle.image %>" class="img-fluid rounded shadow" alt="<%= vehicle.vehicleName %>">
    </div>
    <div class="col-md-6">
        <div class="d-flex justify-content-between align-items-start mb-3">
            <h1 class="mb-0"><%= vehicle.vehicleName %></h1>
            <% if (user && vehicle.createdBy._id.toString() === user.id) { %>
                <div class="btn-group">
                    <a href="/vehicles/<%= vehicle._id %>/edit" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <button type="button" class="btn btn-danger" onclick="confirmDelete('<%= vehicle._id %>', '<%= vehicle.vehicleName %>')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            <% } %>
        </div>

        <div class="mb-3">
            <span class="badge bg-primary fs-6 me-2">
                <i class="fas fa-tag"></i> <%= vehicle.brand %>
            </span>
            <span class="badge bg-success fs-6">
                <i class="fas fa-dollar-sign"></i> $<%= vehicle.price.toLocaleString() %>
            </span>
        </div>

        <div class="mb-4">
            <h5><i class="fas fa-info-circle"></i> Description</h5>
            <p class="text-muted"><%= vehicle.desc %></p>
        </div>

        <div class="mb-4">
            <h6 class="text-muted">
                <i class="fas fa-user"></i> Added by: <%= vehicle.createdBy.username %>
            </h6>
            <small class="text-muted">
                <i class="fas fa-calendar"></i> Created: <%= new Date(vehicle.createdAt).toLocaleDateString() %>
                <% if (vehicle.updatedAt && vehicle.updatedAt !== vehicle.createdAt) { %>
                    | Updated: <%= new Date(vehicle.updatedAt).toLocaleDateString() %>
                <% } %>
            </small>
        </div>

        <div class="d-flex gap-2">
            <a href="/vehicles" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to All Vehicles
            </a>
            <% if (user && vehicle.createdBy._id.toString() === user.id) { %>
                <a href="/vehicles/<%= vehicle._id %>/edit" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Edit Vehicle
                </a>
            <% } %>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="vehicleNameToDelete"></strong>?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(vehicleId, vehicleName) {
    document.getElementById('vehicleNameToDelete').textContent = vehicleName;
    document.getElementById('deleteForm').action = '/vehicles/' + vehicleId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<%- include('../partials/footer') %>
