const LocalStrategy = require('passport-local').Strategy;
const User = require('../models/User');

module.exports = function(passport) {
    // Local Strategy
    passport.use(new LocalStrategy(
        {
            usernameField: 'username',
            passwordField: 'password'
        },
        async (username, password, done) => {
            try {
                // Find user by username
                const user = await User.findOne({ username: username });
                
                if (!user) {
                    return done(null, false, { message: 'Invalid username or password' });
                }
                
                // Check password
                const isMatch = await user.comparePassword(password);
                
                if (!isMatch) {
                    return done(null, false, { message: 'Invalid username or password' });
                }
                
                return done(null, user);
            } catch (error) {
                return done(error);
            }
        }
    ));
    
    // Serialize user for session
    passport.serializeUser((user, done) => {
        done(null, user.id);
    });
    
    // Deserialize user from session
    passport.deserializeUser(async (id, done) => {
        try {
            const user = await User.findById(id);
            done(null, user);
        } catch (error) {
            done(error);
        }
    });
};
