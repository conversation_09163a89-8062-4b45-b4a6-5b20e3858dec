const express = require('express');
const passport = require('passport');
const User = require('../models/User');
const { ensureGuest } = require('../middleware/auth');
const { validateUser, validateLogin, sanitizeInput } = require('../middleware/validation');

const router = express.Router();

// Register page
router.get('/register', ensureGuest, (req, res) => {
    res.render('auth/register');
});

// Register handle
router.post('/register', ensureGuest, sanitizeInput, validateUser, async (req, res) => {
    const { username, email, password, age } = req.body;
    let errors = req.validationErrors || [];

    if (errors.length > 0) {
        res.render('auth/register', {
            errors,
            username,
            email,
            age
        });
    } else {
        try {
            // Check if user already exists
            const existingUser = await User.findOne({
                $or: [{ email: email }, { username: username }]
            });

            if (existingUser) {
                errors.push({ msg: 'User with this email or username already exists' });
                res.render('auth/register', {
                    errors,
                    username,
                    email,
                    age
                });
            } else {
                // Create new user
                const newUser = new User({
                    username,
                    email,
                    password,
                    age: parseInt(age)
                });

                await newUser.save();
                req.flash('success_msg', 'You are now registered and can log in');
                res.redirect('/login');
            }
        } catch (error) {
            console.error(error);
            if (error.name === 'ValidationError') {
                // Handle mongoose validation errors
                Object.values(error.errors).forEach(err => {
                    errors.push({ msg: err.message });
                });
                res.render('auth/register', {
                    errors,
                    username,
                    email,
                    age
                });
            } else {
                req.flash('error_msg', 'An error occurred during registration');
                res.redirect('/register');
            }
        }
    }
});

// Login page
router.get('/login', ensureGuest, (req, res) => {
    res.render('auth/login');
});

// Login handle
router.post('/login', ensureGuest, sanitizeInput, validateLogin, (req, res, next) => {
    const errors = req.validationErrors || [];

    if (errors.length > 0) {
        return res.render('auth/login', {
            errors,
            username: req.body.username
        });
    }

    passport.authenticate('local', {
        successRedirect: '/vehicles',
        failureRedirect: '/login',
        failureFlash: true
    })(req, res, next);
});

// Logout handle
router.get('/logout', (req, res) => {
    req.logout((err) => {
        if (err) {
            console.error(err);
            req.flash('error_msg', 'Error logging out');
            return res.redirect('/vehicles');
        }
        req.flash('success_msg', 'You are logged out');
        res.redirect('/login');
    });
});

module.exports = router;
