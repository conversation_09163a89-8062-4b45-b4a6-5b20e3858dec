# MongoDB Local Setup Guide

## 🎯 Current Status
Your Vehicle CRUD API is **running successfully** at `http://localhost:3000` in **demo mode**.

To enable full database functionality (user registration, vehicle storage), you need to set up MongoDB.

## 🚀 **Option 1: MongoDB Atlas (Recommended - Easiest)**

### Why MongoDB Atlas?
- ✅ **Free tier** (512MB storage)
- ✅ **No installation** required
- ✅ **5-minute setup**
- ✅ **Always available**

### Quick Setup Steps:

1. **Go to MongoDB Atlas**: https://www.mongodb.com/atlas
2. **Sign up** for free account
3. **Create cluster**:
   - Choose **FREE** tier (M0 Sandbox)
   - Select region closest to you
   - Name: "VehicleCluster"
4. **Create database user**:
   - Username: `vehicleapp`
   - Password: `YourPassword123`
   - Permissions: "Read and write to any database"
5. **Network access**:
   - Add IP: `0.0.0.0/0` (for development)
6. **Get connection string**:
   - Click "Connect" → "Connect your application"
   - Copy the connection string
7. **Update .env file**:
   ```env
   MONGODB_URI=mongodb+srv://vehicleapp:<EMAIL>/vehicle_crud_db?retryWrites=true&w=majority
   ```
8. **Restart the app**: `npm run dev`

## 🔧 **Option 2: Local MongoDB Installation**

### For Windows:

1. **Download MongoDB**:
   - Go to: https://www.mongodb.com/try/download/community
   - Select: Windows, MSI package
   - Download and run installer

2. **Install MongoDB**:
   - Run the .msi file
   - Choose "Complete" installation
   - Install as Windows Service
   - Install MongoDB Compass (GUI tool)

3. **Start MongoDB**:
   ```bash
   # MongoDB should start automatically as a service
   # To start manually:
   net start MongoDB
   ```

4. **Verify installation**:
   ```bash
   mongod --version
   ```

5. **Update .env file**:
   ```env
   MONGODB_URI=mongodb://localhost:27017/vehicle_crud_db
   ```

6. **Restart the app**: `npm run dev`

### For macOS:

1. **Install with Homebrew**:
   ```bash
   brew tap mongodb/brew
   brew install mongodb-community
   ```

2. **Start MongoDB**:
   ```bash
   brew services start mongodb/brew/mongodb-community
   ```

3. **Update .env file**:
   ```env
   MONGODB_URI=mongodb://localhost:27017/vehicle_crud_db
   ```

### For Linux (Ubuntu/Debian):

1. **Import MongoDB public key**:
   ```bash
   wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
   ```

2. **Add MongoDB repository**:
   ```bash
   echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
   ```

3. **Install MongoDB**:
   ```bash
   sudo apt-get update
   sudo apt-get install -y mongodb-org
   ```

4. **Start MongoDB**:
   ```bash
   sudo systemctl start mongod
   sudo systemctl enable mongod
   ```

## 🧪 **Testing Your Setup**

Once you've set up MongoDB (Atlas or local), you should see:

```
✅ Connected to MongoDB successfully!
Database features are now available.
✅ Session store configured with MongoDB
Server running on port 3000
```

## 🎮 **What You Can Do After Setup**

With MongoDB connected, you can:

1. **Register new users** at `/register`
2. **Login with credentials** at `/login`
3. **Add vehicles** with full details
4. **Edit your vehicles** (only your own)
5. **Delete vehicles** (only your own)
6. **View all vehicles** from all users

## 🔍 **Troubleshooting**

### Connection Refused Error:
```
Error: connect ECONNREFUSED 127.0.0.1:27017
```
**Solution**: MongoDB is not running. Start MongoDB service.

### Authentication Failed:
```
Error: Authentication failed
```
**Solution**: Check username/password in connection string.

### Network Error (Atlas):
```
Error: querySrv ENOTFOUND
```
**Solution**: Check internet connection and connection string.

## 📱 **Current Demo Mode Features**

Even without MongoDB, you can:
- ✅ View the beautiful responsive interface
- ✅ See all forms and layouts
- ✅ Test the UI/UX
- ✅ Explore the application structure

## 🆘 **Need Help?**

1. **Run setup helper**: `node setup-mongodb.js`
2. **Check documentation**: `README.md`
3. **Atlas documentation**: https://docs.atlas.mongodb.com/

## 🎯 **Recommendation**

For the **fastest setup**, use **MongoDB Atlas**:
- Takes 5 minutes
- No installation required
- Always available
- Free tier sufficient for development

Your application is **production-ready** and just needs a database connection to unlock full functionality!
